const { log } = require('../utils/logger');

class GyroService {
  constructor(rosService) {
    this.rosService = rosService;
    this.resetReadings();
    this.errorCount = 0;
    this.successCount = 0;
    this.lastValidDataTime = null;
    this.dataTimeout = 5000; // 5 sekund timeout dla danych
  }

  resetReadings() {
    this.readings = {
      accel_x: 0,
      accel_y: 0,
      accel_z: 0,
      gyro_x: 0,
      gyro_y: 0,
      gyro_z: 0,
      mag_x: 0,
      mag_y: 0,
      mag_z: 0,
      heading: 0,
      lastUpdated: null,
      isDataValid: false,
      dataStatus: 'no_data' // 'valid', 'stale', 'no_data', 'invalid'
    };
  }

  init() {
    try {
      this.rosService.createSubscriber(
        'std_msgs/msg/Float32MultiArray',
        '/gyro/imu_data',
        this.handleImuData.bind(this)
      );
      
      // Periodyczna kontrola świeżości danych
      this.dataWatchdog = setInterval(() => {
        this.checkDataFreshness();
      }, 1000);
      
      log.success('Zainicjalizowano subskrypcję danych IMU (Float32MultiArray)');
    } catch (error) {
      log.error(`Błąd inicjalizacji subskrypcji IMU: ${error.message}`);
      throw error;
    }
  }

  checkDataFreshness() {
    if (!this.lastValidDataTime) {
      this.readings.dataStatus = 'no_data';
      return;
    }

    const now = Date.now();
    const timeDiff = now - new Date(this.lastValidDataTime).getTime();

    if (timeDiff > this.dataTimeout) {
      this.readings.isDataValid = false;
      this.readings.dataStatus = 'stale';
      log.warn(`Dane IMU nie były aktualizowane od ${timeDiff/1000} sekund`);
    }
  }

  handleImuData(msg) {
    try {
      if (!msg || !msg.data) {
        this.errorCount++;
        this.readings.dataStatus = 'invalid';
        log.warn('Otrzymano pustą wiadomość IMU');
        return;
      }

      if (msg.data.length < 6) {
        this.errorCount++;
        this.readings.dataStatus = 'invalid';
        log.warn(`Niekompletne dane IMU (oczekiwano 6, otrzymano ${msg.data.length})`);
        return;
      }

      // Zoptymalizowana aktualizacja - modyfikacja istniejącego obiektu zamiast tworzenia nowego
      const now = new Date().toISOString();
      this.readings.accel_x = msg.data[0];
      this.readings.accel_y = msg.data[1];
      this.readings.accel_z = msg.data[2];
      this.readings.gyro_x = msg.data[3];
      this.readings.gyro_y = msg.data[4];
      this.readings.gyro_z = msg.data[5];
      this.readings.lastUpdated = now;
      this.readings.isDataValid = true;
      this.readings.dataStatus = 'valid';

      // Dodaj dane magnetometru jeśli dostępne
      if (msg.data.length >= 10) {
        this.readings.mag_x = msg.data[6];
        this.readings.mag_y = msg.data[7];
        this.readings.mag_z = msg.data[8];
        this.readings.heading = msg.data[9];
      }

      this.lastValidDataTime = now;
      this.successCount++;

      // Rzadsze logowanie dla wydajności
      if (this.successCount % 200 === 0) {
        log.info(`Statystyki IMU - Sukcesy: ${this.successCount}, Błędy: ${this.errorCount}`);
      }
    } catch (error) {
      this.errorCount++;
      this.readings.dataStatus = 'invalid';
      log.error(`Błąd przetwarzania danych IMU: ${error.message}`);
      this.resetReadings();
    }
  }

  getReadings() {
    const now = new Date();
    const dataStatus = this.readings.dataStatus;
    
    // Dodatkowa walidacja jeśli dane są oznaczone jako ważne
    if (dataStatus === 'valid') {
      const timeDiff = now - new Date(this.readings.lastUpdated).getTime();
      if (timeDiff > this.dataTimeout) {
        this.readings.isDataValid = false;
        this.readings.dataStatus = 'stale';
      }
    }

    return {
      ...this.readings,
      timestamp: now.toISOString(),
      stats: {
        successCount: this.successCount,
        errorCount: this.errorCount,
        uptime: process.uptime()
      }
    };
  }

  // Metody API z rozszerzoną obsługą braku danych
  getGyroData(req, res) {
    try {
      const data = this.getReadings();
      
      if (data.dataStatus !== 'valid') {
        return res.status(503).json({
          success: false,
          error: this.getDataStatusMessage(data.dataStatus),
          dataStatus: data.dataStatus,
          lastUpdated: data.lastUpdated
        });
      }

      res.json({
        success: true,
        data: {
          accelerometer: {
            x: data.accel_x !== null ? data.accel_x.toFixed(3) : null,
            y: data.accel_y !== null ? data.accel_y.toFixed(3) : null,
            z: data.accel_z !== null ? data.accel_z.toFixed(3) : null,
            unit: 'm/s²'
          },
          gyroscope: {
            x: data.gyro_x !== null ? data.gyro_x.toFixed(2) : null,
            y: data.gyro_y !== null ? data.gyro_y.toFixed(2) : null,
            z: data.gyro_z !== null ? data.gyro_z.toFixed(2) : null,
            unit: '°/s'
          },
          updatedAt: data.lastUpdated,
          dataQuality: data.isDataValid ? 'high' : 'low',
          dataStatus: data.dataStatus
        }
      });
    } catch (error) {
      log.error(`Błąd API getGyroData: ${error.message}`);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        dataStatus: 'error'
      });
    }
  }

  getOrientation(req, res) {
    try {
      const data = this.getReadings();
      
      if (data.dataStatus !== 'valid') {
        return res.status(503).json({
          success: false,
          error: this.getDataStatusMessage(data.dataStatus),
          dataStatus: data.dataStatus,
          lastUpdated: data.lastUpdated
        });
      }

      // Bezpieczne obliczenia orientacji
      const safeValue = (value, fallback = 0.1) => 
        Math.abs(value) > 0.1 ? value : (value >= 0 ? fallback : -fallback);

      const roll = Math.atan2(
        safeValue(data.accel_y), 
        safeValue(data.accel_z)
      ) * (180 / Math.PI);

      const denominator = Math.sqrt(
        Math.pow(data.accel_y, 2) + 
        Math.pow(data.accel_z, 2)
      );
      
      const pitch = Math.atan2(
        -safeValue(data.accel_x),
        safeValue(denominator)
      ) * (180 / Math.PI);

      res.json({
        success: true,
        data: {
          roll: this.formatAngle(roll),
          pitch: this.formatAngle(pitch),
          unit: 'degrees',
          updatedAt: data.lastUpdated,
          orientationQuality: this.calculateOrientationQuality(data),
          dataStatus: data.dataStatus
        }
      });
    } catch (error) {
      log.error(`Błąd API getOrientation: ${error.message}`);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        dataStatus: 'error'
      });
    }
  }

  getMotionStatus(req, res) {
    try {
      const data = this.getReadings();
      
      if (data.dataStatus !== 'valid') {
        return res.json({
          success: true,
          data: {
            isMoving: null,
            accelerationMagnitude: null,
            rotationMagnitude: null,
            movementConfidence: 0,
            updatedAt: data.lastUpdated,
            dataStatus: data.dataStatus,
            warning: this.getDataStatusMessage(data.dataStatus)
          }
        });
      }

      const accelMagnitude = Math.sqrt(
        Math.pow(data.accel_x, 2) +
        Math.pow(data.accel_y, 2) +
        Math.pow(data.accel_z, 2)
      );

      const gyroMagnitude = Math.sqrt(
        Math.pow(data.gyro_x, 2) +
        Math.pow(data.gyro_y, 2) +
        Math.pow(data.gyro_z, 2)
      );

      const isMoving = this.detectMovement(accelMagnitude, gyroMagnitude);

      res.json({
        success: true,
        data: {
          isMoving,
          accelerationMagnitude: accelMagnitude.toFixed(4),
          rotationMagnitude: gyroMagnitude.toFixed(4),
          movementConfidence: this.calculateMovementConfidence(accelMagnitude, gyroMagnitude),
          updatedAt: data.lastUpdated,
          dataStatus: data.dataStatus
        }
      });
    } catch (error) {
      log.error(`Błąd API getMotionStatus: ${error.message}`);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        dataStatus: 'error'
      });
    }
  }

  getSensorStatus(req, res) {
    try {
      const data = this.getReadings();
      const now = new Date();
      const lastUpdate = data.lastUpdated ? new Date(data.lastUpdated) : null;
      
      const uptime = process.uptime();
      const timeSinceLastUpdate = lastUpdate ? (now - lastUpdate) / 1000 : null;

      res.json({
        success: true,
        data: {
          isConnected: data.dataStatus === 'valid',
          lastUpdate: data.lastUpdated,
          timeSinceLastUpdate: timeSinceLastUpdate !== null ? `${timeSinceLastUpdate.toFixed(1)}s` : 'unknown',
          status: this.getStatusText(data.dataStatus),
          uptime: `${Math.floor(uptime / 3600)}h ${Math.floor((uptime % 3600) / 60)}m ${Math.floor(uptime % 60)}s`,
          stats: data.stats,
          dataStatus: data.dataStatus
        }
      });
    } catch (error) {
      log.error(`Błąd API getSensorStatus: ${error.message}`);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        dataStatus: 'error'
      });
    }
  }

  // Metody pomocnicze
  getDataStatusMessage(status) {
    const messages = {
      'no_data': 'Brak danych z czujnika',
      'invalid': 'Nieprawidłowe dane z czujnika',
      'stale': 'Dane z czujnika są nieaktualne',
      'valid': 'Dane poprawne'
    };
    return messages[status] || 'Nieznany status danych';
  }

  getStatusText(status) {
    const statusMap = {
      'valid': 'active',
      'no_data': 'disconnected',
      'invalid': 'error',
      'stale': 'unstable',
      'error': 'fault'
    };
    return statusMap[status] || 'unknown';
  }

  formatAngle(angle) {
    return parseFloat(angle.toFixed(2));
  }

  calculateOrientationQuality(data) {
    if (data.dataStatus !== 'valid') return 'unavailable';
    
    const stability = Math.abs(1 - Math.sqrt(
      Math.pow(data.accel_x, 2) +
      Math.pow(data.accel_y, 2) +
      Math.pow(data.accel_z, 2)
    ) / 9.81);
    
    if (stability < 0.05) return 'high';
    if (stability < 0.1) return 'medium';
    return 'low';
  }

  detectMovement(accelMag, gyroMag) {
    const accelThreshold = 1.5;
    const gyroThreshold = 5;
    const accelVariation = Math.abs(accelMag - 9.81);
    
    return gyroMag > gyroThreshold || accelVariation > accelThreshold;
  }

  calculateMovementConfidence(accelMag, gyroMag) {
    const base = 0.5;
    const accelFactor = Math.min(1, Math.abs(accelMag - 9.81) / 2);
    const gyroFactor = Math.min(1, gyroMag / 10);
    return (base + (accelFactor + gyroFactor) / 2).toFixed(2);
  }

  // Czyszczenie zasobów
  cleanup() {
    if (this.dataWatchdog) {
      clearInterval(this.dataWatchdog);
    }
  }
}

module.exports = GyroService;