const { log } = require('../utils/logger');

class SmokeService {
    constructor(rosService) {
        this.rosService = rosService;
        this.smokeData = {
            concentration: 0,
            gasConcentration: 0,
            alarm: false,
            lastUpdate: null
        };
    }

    init() {
        try {
            // Subskrypcja stężenia dymu
            this.rosService.createSubscriber(
                'std_msgs/msg/Float32',
                '/smoke/concentration',
                (msg) => {
                    this.smokeData.concentration = msg.data;
                    this.smokeData.lastUpdate = new Date();
                    
                    if (msg.data > 300) { // Próg alarmowy
                        log.warn(`High smoke concentration: ${msg.data.toFixed(1)} ppm`);
                    }
                }
            );

            // Subskrypcja stężenia gazów
            this.rosService.createSubscriber(
                'std_msgs/msg/Float32',
                '/smoke/gas_concentration',
                (msg) => {
                    this.smokeData.gasConcentration = msg.data;
                    this.smokeData.lastUpdate = new Date();
                }
            );

            // Subskrypcja alarmu
            this.rosService.createSubscriber(
                'std_msgs/msg/Bool',
                '/smoke/alarm',
                (msg) => {
                    this.smokeData.alarm = msg.data;
                    this.smokeData.lastUpdate = new Date();
                    
                    if (msg.data) {
                        log.error('SMOKE ALARM TRIGGERED!');
                    }
                }
            );

            log.success('Smoke service initialized');
        } catch (err) {
            log.error(`Smoke service init failed: ${err.message}`);
            throw err;
        }
    }

    getSmokeData() {
        return {
            ...this.smokeData,
            status: this.smokeData.lastUpdate ? 'active' : 'inactive',
            lastUpdateAge: this.smokeData.lastUpdate ? 
                Date.now() - this.smokeData.lastUpdate.getTime() : null
        };
    }

    isAlarmActive() {
        return this.smokeData.alarm;
    }

    getSmokeLevel() {
        const concentration = this.smokeData.concentration;
        
        if (concentration < 50) return 'low';
        if (concentration < 200) return 'moderate';
        if (concentration < 300) return 'high';
        return 'critical';
    }
}

module.exports = SmokeService;
