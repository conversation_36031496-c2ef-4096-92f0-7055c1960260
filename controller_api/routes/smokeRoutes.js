const express = require('express');
const { log } = require('../utils/logger');

module.exports = (smokeService) => {
  const router = express.Router();

  // GET /api/smoke - pobierz wszystkie dane czujnika dymu
  router.get('/', (req, res) => {
    try {
      const data = smokeService.getSmokeData();
      res.json(data);
    } catch (err) {
      log.error(`Smoke data error: ${err.message}`);
      res.status(500).json({ error: err.message });
    }
  });

  // GET /api/smoke/concentration - tylko stężenie dymu
  router.get('/concentration', (req, res) => {
    try {
      const data = smokeService.getSmokeData();
      res.json({
        concentration: data.concentration,
        level: smokeService.getSmokeLevel(),
        lastUpdate: data.lastUpdate
      });
    } catch (err) {
      log.error(`Smoke concentration error: ${err.message}`);
      res.status(500).json({ error: err.message });
    }
  });

  // GET /api/smoke/gas - tylko stężenie gazów
  router.get('/gas', (req, res) => {
    try {
      const data = smokeService.getSmokeData();
      res.json({
        gasConcentration: data.gasConcentration,
        lastUpdate: data.lastUpdate
      });
    } catch (err) {
      log.error(`Gas concentration error: ${err.message}`);
      res.status(500).json({ error: err.message });
    }
  });

  // GET /api/smoke/alarm - status alarmu
  router.get('/alarm', (req, res) => {
    try {
      const data = smokeService.getSmokeData();
      res.json({
        alarm: data.alarm,
        isActive: smokeService.isAlarmActive(),
        level: smokeService.getSmokeLevel(),
        lastUpdate: data.lastUpdate
      });
    } catch (err) {
      log.error(`Smoke alarm error: ${err.message}`);
      res.status(500).json({ error: err.message });
    }
  });

  // GET /api/smoke/status - status czujnika
  router.get('/status', (req, res) => {
    try {
      const data = smokeService.getSmokeData();
      const isOnline = data.lastUpdateAge !== null && data.lastUpdateAge < 5000; // 5 sekund
      
      res.json({
        online: isOnline,
        status: data.status,
        lastUpdate: data.lastUpdate,
        lastUpdateAge: data.lastUpdateAge
      });
    } catch (err) {
      log.error(`Smoke status error: ${err.message}`);
      res.status(500).json({ error: err.message });
    }
  });

  return router;
};
