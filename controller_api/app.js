const express = require('express');
const app = express();
const port = process.env.PORT || 3000;
const rosService = require('./services/rosService');
const { log } = require('./utils/logger');
const TemperatureService = require('./services/temperatureService');
const PowerService = require('./services/powerService');
const GyroService = require('./services/gyroService');
const AdsService = require('./services/adsService');
const SmokeService = require('./services/smokeService');

async function setup() {
  try {
    // Initialize ROS node
    const rosNode = await rosService.init();
    app.set('rosNode', rosNode);
    rosService.start();

    const temperatureService = new TemperatureService(rosService);
    temperatureService.init();

    const powerService = new PowerService(rosService);
    powerService.init();

    const gyroService = new GyroService(rosService);
    gyroService.init();

    const adsService = new AdsService(rosService);
    adsService.init();

    const smokeService = new SmokeService(rosService);
    smokeService.init();

    // Setup routes
    const apiRoutes = require('./routes/apiRoutes');
    app.use('/api', apiRoutes);



    const rosRoutes = require('./routes/rosRoutes')(rosService);
    app.use('/api/ros', rosRoutes);

    const temperatureRoutes = require('./routes/temperatureRoutes')(temperatureService);
    app.use('/api/temperatures', temperatureRoutes);

    const powerRoutes = require('./routes/powerRoutes')(powerService);
    app.use('/api/power', powerRoutes);

    const gyroRoutes = require('./routes/gyroRoutes')(gyroService);
    app.use('/api/gyro', gyroRoutes);

    const smokeRoutes = require('./routes/smokeRoutes')(smokeService);
    app.use('/api/smoke', smokeRoutes);




    // Start server
    app.listen(port, () => {
      log.success(`Server running on port ${port}`);
    });

    // Graceful shutdown
    process.on('SIGINT', async () => {
      await rosService.shutdown();
      process.exit(0);
    });

  } catch (err) {
    log.error(`Application failed: ${err.message}`);
    process.exit(1);
  }
}

setup();