from launch import LaunchDescription
from launch_ros.actions import Node
from launch.actions import DeclareLaunchArgument, LogInfo
from launch.substitutions import LaunchConfiguration


def generate_launch_description():
    return LaunchDescription([
        # Launch arguments
        DeclareLaunchArgument(
            'log_level',
            default_value='info',
            description='Logging level'
        ),
        
        # Log startup message
        LogInfo(msg="Starting ARGUS Robot Core System..."),
        
        # OLED Display Node
        Node(
            package='oled_subscriber',
            namespace='oled',
            executable='oled',
            name='oled_display',
            output='screen',
            parameters=[{
                'log_level': LaunchConfiguration('log_level')
            }]
        ),
        
        # Temperature Sensor Node
        Node(
            package='sensors',
            namespace='sensors',
            executable='ads',
            name='temperature_monitor',
            output='screen',
            parameters=[{
                'log_level': LaunchConfiguration('log_level')
            }]
        ),
        
        # Motor Driver Node
        Node(
            package='py_motors',
            namespace='motors',
            executable='pwm',
            name='motor_driver',
            output='screen',
            parameters=[{
                'log_level': LaunchConfiguration('log_level')
            }]
        ),
        
        # Power Monitor Node
        Node(
            package='sensors',
            namespace='sensors',
            executable='power',
            name='power_monitor',
            output='screen',
            parameters=[{
                'log_level': LaunchConfiguration('log_level')
            }]
        ),
        
        # Gyroscope Node
        Node(
            package='sensors',
            namespace='sensors',
            executable='gyro',
            name='gyro_monitor',
            output='screen',
            parameters=[{
                'log_level': LaunchConfiguration('log_level')
            }]
        ),
        
        # Camera Node
        Node(
            package='camera_node',
            namespace='camera',
            executable='camera_publisher',
            name='camera_node',
            output='screen',
            parameters=[{
                'log_level': LaunchConfiguration('log_level'),
                'camera_device': '/dev/video0',
                'frame_rate': 30,
                'image_width': 640,
                'image_height': 480
            }]
        ),
        
        LogInfo(msg="ARGUS Robot Core System started successfully!")
    ])
