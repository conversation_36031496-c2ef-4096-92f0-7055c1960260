<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>robot_core</name>
  <version>1.0.0</version>
  <description>ARGUS Robot Core Package with Launch Files</description>
  <maintainer email="<EMAIL>">ARGUS Team</maintainer>
  <license>MIT</license>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <exec_depend>launch</exec_depend>
  <exec_depend>launch_ros</exec_depend>
  <exec_depend>oled_subscriber</exec_depend>
  <exec_depend>sensors</exec_depend>
  <exec_depend>py_motors</exec_depend>
  <exec_depend>camera_node</exec_depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
