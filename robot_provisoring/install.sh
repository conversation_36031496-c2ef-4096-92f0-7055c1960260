#!/bin/bash

# ARGUS Robot Provisioning Script
# Automatyczna instalacja i konfiguracja robota ARGUS na Raspberry Pi
# Wymaga: Raspbian Bookworm, połączenie internetowe (preferowane LAN)

set -e  # Zatrzymaj przy błędzie

# Kolory dla outputu
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Ścieżki
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SCRIPTS_DIR="$SCRIPT_DIR/scripts"
FILES_DIR="$SCRIPT_DIR/files"
SERVICES_DIR="$SCRIPT_DIR/services"
NETWORK_DIR="$SCRIPT_DIR/network"

# Zmienne konfiguracyjne
ROS2_INSTALL_DIR="/root"
ARGUS_WORKSPACE="$ROS2_INSTALL_DIR/argus_ws"

# Funkcje pomocnicze
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Funkcja sprawdzająca wersję Raspbiana
check_raspbian_version() {
    if [ ! -f /etc/os-release ]; then
        log_error "Nie znaleziono pliku /etc/os-release"
        return 1
    fi

    local os_id=$(grep '^ID=' /etc/os-release | cut -d '=' -f 2 | tr -d '"')
    local os_version=$(grep '^VERSION_ID=' /etc/os-release | cut -d '=' -f 2 | tr -d '"')
    local os_name=$(grep '^PRETTY_NAME=' /etc/os-release | cut -d '=' -f 2 | tr -d '"')

    if [ "$os_id" != "raspbian" ] && [ "$os_id" != "debian" ]; then
        log_error "To nie jest Raspbian/Debian! To jest: $os_name"
        return 1
    fi

    if [ "$os_version" == "12" ] || echo "$os_name" | grep -qi "bookworm"; then
        log_success "System to Raspbian Bookworm ($os_name)"
        return 0
    else
        log_error "To nie jest Raspbian Bookworm. Obecna wersja: $os_name"
        return 1
    fi
}

# Funkcja sprawdzająca uprawnienia root
check_root() {
    if [ "$EUID" -ne 0 ]; then
        log_error "Ten skrypt musi być uruchomiony jako root (sudo)"
        exit 1
    fi
}

# Funkcja sprawdzająca połączenie internetowe
check_internet() {
    log_info "Sprawdzanie połączenia internetowego..."
    if ping -c 1 google.com &> /dev/null; then
        log_success "Połączenie internetowe działa"
        return 0
    else
        log_error "Brak połączenia internetowego"
        return 1
    fi
}

# Funkcja wykonująca skrypt z logowaniem
run_script() {
    local script_name="$1"
    local script_path="$SCRIPTS_DIR/$script_name"

    if [ ! -f "$script_path" ]; then
        log_error "Skrypt $script_name nie istnieje w $script_path"
        return 1
    fi

    log_info "Wykonywanie skryptu: $script_name"
    chmod +x "$script_path"

    if bash "$script_path"; then
        log_success "Skrypt $script_name wykonany pomyślnie"
        return 0
    else
        log_error "Błąd podczas wykonywania skryptu $script_name"
        return 1
    fi
}

# Funkcja wyświetlająca ostrzeżenia
show_warnings() {
    echo ""
    log_warning "=============================================="
    log_warning "           WAŻNE OSTRZEŻENIA!"
    log_warning "=============================================="
    log_warning "1. PODCZAS INSTALACJI NASTĄPI ROZŁĄCZENIE Z SIECIĄ WIFI"
    log_warning "2. ZALECANE PODŁĄCZENIE ZA POMOCĄ LAN"
    log_warning "3. NIE ZASTOSOWANIE LAN MOŻE WYMAGAĆ REINSTALACJI"
    log_warning "4. PROCES MOŻE POTRWAĆ 30-60 MINUT"
    log_warning "5. NIE PRZERYWAJ PROCESU INSTALACJI"
    log_warning "=============================================="
    echo ""
}

# Funkcja główna instalacji
install_argus() {
    log_info "Rozpoczynanie instalacji ARGUS Robot..."

    # Kolejność wykonywania skryptów
    local scripts=(
        "01-base.sh"
        "02-env.sh"
        "03-deps.sh"
        "04-ros2.sh"
        "05-wan.sh"
        "06-services.sh"
    )

    for script in "${scripts[@]}"; do
        if ! run_script "$script"; then
            log_error "Instalacja przerwana z powodu błędu w skrypcie $script"
            exit 1
        fi
        sleep 2  # Krótka pauza między skryptami
    done

    log_success "Instalacja ARGUS Robot zakończona pomyślnie!"
    log_info "System zostanie zrestartowany za 10 sekund..."
    sleep 10
    reboot
}

# Funkcja główna
main() {
    clear
    echo -e "${BLUE}"
    echo "  █████╗ ██████╗  ██████╗ ██╗   ██╗███████╗"
    echo " ██╔══██╗██╔══██╗██╔════╝ ██║   ██║██╔════╝"
    echo " ███████║██████╔╝██║  ███╗██║   ██║███████╗"
    echo " ██╔══██║██╔══██╗██║   ██║██║   ██║╚════██║"
    echo " ██║  ██║██║  ██║╚██████╔╝╚██████╔╝███████║"
    echo " ╚═╝  ╚═╝╚═╝  ╚═╝ ╚═════╝  ╚═════╝ ╚══════╝"
    echo -e "${NC}"
    echo "        Robot Provisioning System v1.0"
    echo ""

    # Sprawdzenia wstępne
    check_root

    if ! check_raspbian_version; then
        exit 1
    fi

    if ! check_internet; then
        log_error "Wymagane jest połączenie internetowe do instalacji"
        exit 1
    fi

    show_warnings

    # Potwierdzenie od użytkownika
    read -p "Czy chcesz kontynuować instalację? (tak/nie): " -r
    if [[ ! $REPLY =~ ^[Tt]ak$ ]]; then
        log_info "Instalacja anulowana przez użytkownika"
        exit 0
    fi

    # Rozpocznij instalację
    install_argus
}

main "$@"

