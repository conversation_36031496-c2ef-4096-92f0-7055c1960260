#!/bin/bash
# 04-ros2.sh - Instalacja ROS2 Jazzy i konfiguracja workspace

set -e

echo "=== Instalacja ROS2 Jazzy ==="

# Dodanie klucza GPG dla ROS2
echo "Dodawanie klucza GPG dla ROS2..."
curl -sSL https://raw.githubusercontent.com/ros/rosdistro/master/ros.key -o /usr/share/keyrings/ros-archive-keyring.gpg

# Dodanie repozytorium ROS2 dla Debian
echo "Dodawanie repozytorium ROS2..."
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/ros-archive-keyring.gpg] http://packages.ros.org/ros2/ubuntu jammy main" | tee /etc/apt/sources.list.d/ros2.list > /dev/null

# Aktualizacja listy pakietów
echo "Aktualizacja listy pakietów..."
apt update

# Instalacja ROS2 Jazzy Desktop
echo "Instalacja ROS2 Jazzy (może potrwać 15-30 minut)..."
apt install -y ros-jazzy-ros-base

# Instalacja dodatkowych pakietów ROS2
echo "Instalacja dodatkowych pakietów ROS2..."
apt install -y \
    ros-jazzy-cv-bridge \
    ros-jazzy-image-transport \
    ros-jazzy-camera-info-manager \
    ros-jazzy-compressed-image-transport \
    ros-jazzy-rqt-image-view \
    ros-jazzy-usb-cam \
    ros-jazzy-v4l2-camera \
    ros-jazzy-robot-state-publisher \
    ros-jazzy-joint-state-publisher \
    ros-jazzy-xacro \
    ros-jazzy-tf2-tools \
    ros-jazzy-tf2-ros \
    ros-jazzy-geometry-msgs \
    ros-jazzy-sensor-msgs \
    ros-jazzy-std-msgs \
    ros-jazzy-nav-msgs \
    ros-jazzy-visualization-msgs

# Instalacja narzędzi do budowania
echo "Instalacja narzędzi do budowania..."
apt install -y \
    python3-colcon-common-extensions \
    python3-rosdep \
    python3-vcstool

# Inicjalizacja rosdep
echo "Inicjalizacja rosdep..."
rosdep init || true
rosdep update

# Konfiguracja środowiska ROS2 w .bashrc dla root
echo "Konfiguracja środowiska ROS2..."
cat >> /root/.bashrc << 'EOF'

# ROS2 Jazzy Environment
source /opt/ros/jazzy/setup.bash
source /root/ros2_jazzy/install/setup.bash 2>/dev/null || true

# ROS2 Domain ID dla ARGUS
export ROS_DOMAIN_ID=42

# ROS2 Localhost only (bezpieczeństwo)
export ROS_LOCALHOST_ONLY=1

EOF

echo "=== Instalacja ROS2 Jazzy zakończona ==="