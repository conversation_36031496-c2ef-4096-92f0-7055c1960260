#!/bin/bash
# 03-deps.sh - Instalacja zależności Python i bibliotek

set -e

echo "=== Instalacja zależności Python i bibliotek ==="

# Instalacja pakietów Python przez apt (preferowane dla systemowych instalacji)
echo "Instalacja podstawowych bibliotek Python przez apt..."
apt install -y \
    python3-pip \
    python3-numpy \
    python3-opencv \
    python3-pil \
    python3-serial \
    python3-smbus \
    python3-rpi.gpio

# Tworzenie wirtualnego środowiska dla dodatkowych pakietów
echo "Tworzenie wirtualnego środowiska Python..."
python3 -m venv /opt/argusik/venv --system-site-packages

# Aktywacja venv i instalacja dodatkowych pakietów
echo "Instalacja dodatkowych bibliotek Python w venv..."
source /opt/argusik/venv/bin/activate

# Aktualizacja pip w venv
pip install --upgrade pip

# Instalacja bibliotek Adafruit
pip install \
    adafruit-circuitpython-motor \
    adafruit-circuitpython-servokit \
    adafruit-circuitpython-pca9685 \
    adafruit-blinka \
    board \
    busio \
    digitalio

# Instalacja bibliotek do obsługi kamer i streamingu
echo "Instalacja bibliotek do obsługi kamer..."
pip install \
    picamera2 \
    av \
    aiortc \
    websockets \
    flask \
    flask-socketio

# Instalacja bibliotek do obsługi wyświetlaczy OLED
echo "Instalacja bibliotek OLED..."
pip install \
    luma.oled \
    luma.core \
    luma.emulator

# Instalacja bibliotek do obsługi sensorów
echo "Instalacja bibliotek sensorów..."
pip install \
    w1thermsensor \
    mpu6050-raspberrypi \
    bmp280 \
    adafruit-circuitpython-bmp280 \
    adafruit-circuitpython-mpu6050

# Deaktywacja venv
deactivate

# Instalacja dodatkowych narzędzi
echo "Instalacja dodatkowych narzędzi..."
apt install -y \
    ffmpeg \
    v4l-utils \
    motion \
    supervisor

# Konfiguracja uprawnień dla urządzeń
echo "Konfiguracja uprawnień urządzeń..."
usermod -a -G video,audio,dialout root
usermod -a -G video,audio,dialout argusik 2>/dev/null || true

# Tworzenie linków symbolicznych dla urządzeń
echo "Konfiguracja urządzeń..."
# Dodanie reguł udev dla stabilnych nazw urządzeń
cat > /etc/udev/rules.d/99-argus-devices.rules << 'EOF'
# ARGUS Robot device rules
SUBSYSTEM=="video4linux", ATTRS{idVendor}=="*", ATTRS{idProduct}=="*", SYMLINK+="argus_camera%n"
SUBSYSTEM=="tty", ATTRS{idVendor}=="1a86", ATTRS{idProduct}=="7523", SYMLINK+="argus_serial"
EOF

# Przeładowanie reguł udev
udevadm control --reload-rules
udevadm trigger

echo "=== Instalacja zależności zakończona ==="