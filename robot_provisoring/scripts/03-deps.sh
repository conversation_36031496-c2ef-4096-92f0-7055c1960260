#!/bin/bash
# 03-deps.sh - Instalacja zależności Python i bibliotek

set -e

echo "=== Instalacja zależności Python i bibliotek ==="

# Aktualizacja pip
echo "Aktualizacja pip..."
python3 -m pip install --upgrade pip

# Instalacja podstawowych bibliotek Python
echo "Instalacja podstawowych bibliotek Python..."
pip3 install \
    numpy \
    opencv-python \
    Pillow \
    pyserial \
    smbus2 \
    RPi.GPIO \
    adafruit-circuitpython-motor \
    adafruit-circuitpython-servokit \
    adafruit-circuitpython-pca9685 \
    board \
    busio \
    digitalio \
    adafruit-blinka

# Instalacja bibliotek do obsługi kamer i streamingu
echo "Instalacja bibliotek do obsługi kamer..."
pip3 install \
    picamera2 \
    av \
    aiortc \
    websockets \
    flask \
    flask-socketio

# Instalacja bibliotek do obsługi wyświetlaczy OLED
echo "Instalacja bibliotek OLED..."
pip3 install \
    luma.oled \
    luma.core \
    luma.emulator

# Instalacja bibliotek do obsługi sensorów
echo "Instalacja bibliotek sensorów..."
pip3 install \
    w1thermsensor \
    mpu6050-raspberrypi \
    bmp280 \
    adafruit-circuitpython-bmp280 \
    adafruit-circuitpython-mpu6050

# Instalacja dodatkowych narzędzi
echo "Instalacja dodatkowych narzędzi..."
apt install -y \
    ffmpeg \
    v4l-utils \
    motion \
    supervisor

# Konfiguracja uprawnień dla urządzeń
echo "Konfiguracja uprawnień urządzeń..."
usermod -a -G video,audio,dialout root
usermod -a -G video,audio,dialout argus

# Tworzenie linków symbolicznych dla urządzeń
echo "Konfiguracja urządzeń..."
# Dodanie reguł udev dla stabilnych nazw urządzeń
cat > /etc/udev/rules.d/99-argus-devices.rules << 'EOF'
# ARGUS Robot device rules
SUBSYSTEM=="video4linux", ATTRS{idVendor}=="*", ATTRS{idProduct}=="*", SYMLINK+="argus_camera%n"
SUBSYSTEM=="tty", ATTRS{idVendor}=="1a86", ATTRS{idProduct}=="7523", SYMLINK+="argus_serial"
EOF

# Przeładowanie reguł udev
udevadm control --reload-rules
udevadm trigger

echo "=== Instalacja zależności zakończona ==="