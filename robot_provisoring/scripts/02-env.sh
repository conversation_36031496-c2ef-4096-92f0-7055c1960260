#!/bin/bash
# 02-env.sh - Konfiguracja środowiska i zmiennych

set -e

echo "=== Konfiguracja środowiska ==="

# Tworzenie katalogów roboczych
echo "Tworzenie katalogów roboczych..."
mkdir -p /root/ros2_jazzy/src          # ROS2 workspace
mkdir -p /root/src_ros2                # Kod źródłowy z GitLab
mkdir -p /root/logs
mkdir -p /root/config
mkdir -p /opt/argusik

# Konfiguracja zmiennych środowiskowych w .bashrc dla root
echo "Konfiguracja zmiennych środowiskowych..."
cat >> /root/.bashrc << 'EOF'

# ARGUS Robot Environment Variables
export ARGUS_ROOT="/root"
export ARGUS_SRC="/root/src_ros2"           # Kod źródłowy z GitLab
export ARGUS_WS="/root/ros2_jazzy"          # ROS2 workspace
export ARGUS_CONFIG="/root/config"
export ARGUS_LOGS="/root/logs"

# Python Virtual Environment
source /opt/argusik/venv/bin/activate 2>/dev/null || true

# ROS2 Environment (będzie dodane po instalacji ROS2)
# source /opt/ros/jazzy/setup.bash
# source $ARGUS_WS/install/setup.bash

# Python path dla ARGUS
export PYTHONPATH="$ARGUS_SRC:$ARGUS_WS/src:$PYTHONPATH"

# Aliasy dla ARGUS
alias argus-build='cd $ARGUS_WS && colcon build'
alias argus-source='source $ARGUS_WS/install/setup.bash'
alias argus-logs='cd $ARGUS_LOGS'
alias argus-ws='cd $ARGUS_WS'
alias argus-src='cd $ARGUS_SRC'

EOF

# Konfiguracja podstawowych aliasów dla użytkownika argus (tylko do zarządzania)
echo "Konfiguracja aliasów zarządzania dla użytkownika argus..."
cat >> /home/<USER>/.bashrc << 'EOF'

# ARGUS Robot Management Aliases
alias argus-status='sudo systemctl status argus_core'
alias argus-restart='sudo systemctl restart argus_core'
alias argus-stop='sudo systemctl stop argus_core'
alias argus-start='sudo systemctl start argus_core'
alias argus-logs='sudo journalctl -u argus_core -f'
alias argus-diag='sudo /opt/argusik/bin/argus_diagnostics.sh'

EOF

# Ustawienie właściciela dla katalogu użytkownika argus
chown argusik:argusik /home/<USER>/.bashrc 2>/dev/null || true

# Konfiguracja locale
echo "Konfiguracja locale..."
# Najpierw sprawdź czy locale jest dostępne w /etc/locale.gen i odkomentuj
if grep -q "^# pl_PL.UTF-8" /etc/locale.gen; then
    echo "Odkomentowywanie pl_PL.UTF-8 w /etc/locale.gen"
    sed -i 's/^# pl_PL\.UTF-8 UTF-8/pl_PL.UTF-8 UTF-8/' /etc/locale.gen
fi
# Wygeneruj polskie locale
locale-gen pl_PL.UTF-8
# Ustaw jako domyślne
update-locale LANG=pl_PL.UTF-8

# Konfiguracja timezone
echo "Konfiguracja timezone..."
timedatectl set-timezone Europe/Warsaw

# Konfiguracja hostname
echo "Konfiguracja hostname..."
hostnamectl set-hostname argus-robot

# Aktualizacja /etc/hosts
echo "*********    argus-robot" >> /etc/hosts

echo "=== Konfiguracja środowiska zakończona ==="