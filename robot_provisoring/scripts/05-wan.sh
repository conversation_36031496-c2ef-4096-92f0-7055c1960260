#!/bin/bash
# 05-wan.sh - Konfiguracja sieci i Access Point

set -e

echo "=== Konfiguracja sieci i Access Point ==="

# Instalacja pakietów do obsługi Access Point
echo "Instalacja pakietów sieciowych..."
apt install -y \
    hostapd \
    dnsmasq \
    iptables-persistent \
    bridge-utils

# Zatrzymanie usług przed konfiguracją
echo "Zatrzymywanie usług..."
systemctl stop hostapd
systemctl stop dnsmasq

# Konfiguracja hostapd
echo "Konfiguracja hostapd..."
cat > /etc/hostapd/hostapd.conf << 'EOF'
# ARGUS Robot Access Point Configuration
interface=wlan0
driver=nl80211
ssid=ARGUS
hw_mode=g
channel=7
wmm_enabled=0
macaddr_acl=0
auth_algs=1
ignore_broadcast_ssid=0
wpa=2
wpa_passphrase=argusik!23
wpa_key_mgmt=WPA-PSK
wpa_pairwise=TKIP
rsn_pairwise=CCMP
EOF

# Konfiguracja domyślnego pliku hostapd
echo 'DAEMON_CONF="/etc/hostapd/hostapd.conf"' >> /etc/default/hostapd

# Konfiguracja dnsmasq
echo "Konfiguracja dnsmasq..."
cp /etc/dnsmasq.conf /etc/dnsmasq.conf.orig
cat > /etc/dnsmasq.conf << 'EOF'
# ARGUS Robot DHCP Configuration
interface=wlan0
dhcp-range=***********,***********0,*************,24h
EOF

# Konfiguracja statycznego IP dla wlan0
echo "Konfiguracja statycznego IP..."
cat >> /etc/dhcpcd.conf << 'EOF'

# ARGUS Robot Access Point Configuration
interface wlan0
    static ip_address=***********/24
    nohook wpa_supplicant
EOF

# Konfiguracja przekierowania ruchu (NAT)
echo "Konfiguracja NAT..."
cat > /etc/sysctl.d/routed-ap.conf << 'EOF'
# Enable IPv4 routing
net.ipv4.ip_forward=1
EOF

# Konfiguracja iptables
echo "Konfiguracja iptables..."
iptables -t nat -A POSTROUTING -o eth0 -j MASQUERADE
iptables -A FORWARD -i eth0 -o wlan0 -m state --state RELATED,ESTABLISHED -j ACCEPT
iptables -A FORWARD -i wlan0 -o eth0 -j ACCEPT

# Zapisanie reguł iptables
netfilter-persistent save

# Włączenie usług
echo "Włączanie usług..."
systemctl unmask hostapd
systemctl enable hostapd
systemctl enable dnsmasq

# Kopiowanie pliku konfiguracyjnego sieci
PROVISION_DIR="$(dirname "$(dirname "$(realpath "$0")")")"
mkdir -p /etc/argus
if [ -f "$PROVISION_DIR/network/network.conf" ]; then
    echo "Kopiowanie konfiguracji sieci..."
    cp "$PROVISION_DIR/network/network.conf" /etc/argus/
fi

echo "=== Konfiguracja sieci zakończona ==="
echo "UWAGA: Po restarcie robot będzie dostępny jako Access Point:"
echo "SSID: ARGUS"
echo "Hasło: argusik!23"
echo "IP: ***********"