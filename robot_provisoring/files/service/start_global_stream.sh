#!/bin/bash

# Konfiguracja
DEVICE="/dev/video16"
WIDTH=1280
HEIGHT=720
FPS=30
PORT=8554
IP=$(hostname -I | awk '{print $1}')
STREAM_NAME="tempVision"

# Ścieżka do rtsp-simple-server
RTSP_SERVER="./rtsp-simple-server"

# Upewnij się, że zmienne nie zawierają spacji
STREAM_NAME=$(echo "$STREAM_NAME" | tr -d ' ')
IP=$(echo "$IP" | tr -d ' ')

# Uruchom serwer RTSP w tle
$RTSP_SERVER &

# Poczekaj na inicjalizację serwera
sleep 2

# Uruchom strumień
ffmpeg -f v4l2 -input_format yuyv422 \
    -video_size "${WIDTH}x${HEIGHT}" \
    -framerate "$FPS" \
    -i "$DEVICE" \
    -fflags nobuffer \
    -flags low_delay \
    -tune zerolatency \
    -preset ultrafast \
    -crf 23 \
    -g "$FPS" \
    -pix_fmt yuv420p \
    -c:v libx264 -x264-params "nal-hrd=cbr:force-cfr=1:no-scenecut=1" \
    -f rtsp \
    -rtsp_transport tcp \
    "rtsp://${IP}:${PORT}/${STREAM_NAME}"
