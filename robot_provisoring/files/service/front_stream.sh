#!/bin/bash

RTSP_PORT=8554
STREAM_NAME="camera_stream"
RESOLUTION="1280x720"
FPS=30
BITRATE="1000k"  # 1 Mbps
VIDEO_DEVICE="/dev/video0"

# Sprawdź, czy ffmpeg jest zainstalowany
if ! command -v ffmpeg &> /dev/null; then
    echo "Zainstaluj ffmpeg: sudo apt install ffmpeg"
    exit 1
fi

# Sprawdź, czy urządzenie kamery istnieje
if [ ! -e "$VIDEO_DEVICE" ]; then
    echo "Błąd: Nie znaleziono kamery $VIDEO_DEVICE"
    exit 1
fi

echo "Uruchamianie strumienia RTSP na rtsp://localhost:$RTSP_PORT/$STREAM_NAME"

ffmpeg \
    -f v4l2 \
    -input_format h264 \
    -video_size "$RESOLUTION" \
    -framerate "$FPS" \
    -i "$VIDEO_DEVICE" \
    -c:v copy \
    -f rtsp \
    -rtsp_transport tcp \
    "rtsp://localhost:$RTSP_PORT/$STREAM_NAME"
