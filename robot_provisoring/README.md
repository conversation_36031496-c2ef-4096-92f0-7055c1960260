# ARGUS Robot Provisioning 🤖

Automatyczny system provisioningu dla robota ARGUS na Raspberry Pi z ROS2 Jazzy.

## 🎯 Cel

Pełna automatyzacja konfiguracji robota ARGUS:
- Instalacja ROS2 Jazzy w `/root/`
- Konfiguracja wszystkich zależności
- Uruchomienie usług systemowych
- Konfiguracja Access Point
- Gotowy do użycia robot po jednym poleceniu

## 🛠 Wymagania

- **Raspberry Pi 4** (zalecane 4GB RAM lub więcej)
- **Raspbian Bookworm** (Debian 12)
- **Karta microSD** (minimum 32GB, zalecane 64GB)
- **Połączenie internetowe** (preferowane LAN podczas instalacji)

## 🚀 Szybka instalacja

1. **Pobierz projekt:**
   ```bash
   git clone <repository-url>
   cd robot_provisioning
   ```

2. **Skonfiguruj GitLab (opcjonalne):**
   ```bash
   # Edytuj URL repozytorium robot_core
   nano config/gitlab.conf
   ```

3. **Uruchom instalację:**
   ```bash
   sudo ./install.sh
   ```

3. **Poczekaj na zakończenie** (30-60 minut)

4. **System zostanie automatycznie zrestartowany**

## 📦 Co zostanie zainstalowane

### System i podstawowe pakiety
- Aktualizacja systemu Raspbian Bookworm
- Podstawowe narzędzia deweloperskie
- Biblioteki do obsługi kamer i sensorów
- Konfiguracja interfejsów I2C, SPI, Camera

### ROS2 Jazzy
- Pełna instalacja ROS2 Jazzy Desktop
- Dodatkowe pakiety ROS2 (cv_bridge, camera drivers, etc.)
- Workspace w `/root/argus_ws`
- Automatyczne sourcing środowiska

### Python i biblioteki
- OpenCV, NumPy, Pillow
- Biblioteki Adafruit do obsługi hardware
- Biblioteki do obsługi kamer (picamera2)
- Biblioteki do streamingu (ffmpeg, rtsp)

### Sieć i Access Point
- Konfiguracja Access Point (SSID: ARGUS-Robot)
- DHCP server (***********/24)
- NAT forwarding
- SSH server

### Usługi systemowe
- `argus_core.service` - główna usługa ROS2
- Automatyczne uruchamianie przy starcie
- Logowanie do `/var/log/argus/`

## 📂 Struktura katalogów

```
/root/
├── ros2_jazzy/         # ROS2 workspace
│   ├── src/           # Skompilowany kod ROS2
│   ├── build/         # Pliki build
│   └── install/       # Zainstalowane pakiety
├── src_ros2/          # Kod źródłowy z GitLab
│   └── robot_core/    # Projekt robot_core
├── config/            # Pliki konfiguracyjne
└── logs/              # Logi aplikacji

/opt/argus/
├── bin/               # Skrypty wykonywalne
└── config/            # Konfiguracja systemowa
```

### 🔍 Opis struktury provisioningu

1. **files/** - gotowe pliki konfiguracyjne:
   - `launcher/` - launch files ROS2
   - `service/` - pliki streamingu RTSP
2. **scripts/** - skrypty wykonawcze:
   - `01-base.sh` - podstawowa konfiguracja
   - `02-env.sh` - środowisko
   - `03-deps.sh` - zależności Python
   - `04-ros2.sh` - instalacja ROS2
   - `05-wan.sh` - konfiguracja sieci
   - `06-services.sh` - usługi systemowe
3. **network/** - konfiguracje sieciowe
4. **services/** - pliki systemd
5. **config/** - konfiguracja GitLab i inne ustawienia

## ⚙️ Konfiguracja GitLab

Przed instalacją możesz skonfigurować automatyczne pobieranie kodu robot_core z GitLab:

```bash
# Edytuj plik konfiguracyjny
nano config/gitlab.conf
```

Ustaw właściwy URL repozytorium:
```bash
ROBOT_CORE_REPO_URL="https://gitlab.com/twoja-nazwa/robot_core.git"
ROBOT_CORE_BRANCH="main"
```

**Dla repozytoriów prywatnych** dodaj dane uwierzytelniające:
```bash
GITLAB_USERNAME="twoja-nazwa"
GITLAB_TOKEN="twoj-token"
```

Jeśli nie skonfigurujesz GitLab, system użyje lokalnego kodu z katalogu `../robot_core` jako fallback.

## 🌐 Dostęp do robota

Po instalacji robot będzie dostępny jako Access Point:

- **SSID:** `ARGUS-Robot`
- **Hasło:** `argus2024`
- **IP robota:** `***********`
- **SSH:** `ssh argus@***********` (hasło: argus2024)

## 🔧 Zarządzanie usługami

```bash
# Status usługi głównej
sudo systemctl status argus_core

# Restart usługi
sudo systemctl restart argus_core

# Logi usługi
sudo journalctl -u argus_core -f

# Diagnostyka
sudo /opt/argus/bin/argus_diagnostics.sh
```

## 🎮 Aliasy systemowe

Po instalacji dostępne będą aliasy:

```bash
# Dla użytkownika root
argus-build      # Budowanie workspace ROS2
argus-source     # Source środowiska ROS2
argus-ws         # Przejście do workspace ROS2
argus-src        # Przejście do kodu źródłowego
argus-logs       # Przejście do logów
argus-diag       # Diagnostyka systemu

# Dla użytkownika argus (zarządzanie)
argus-status     # Status usługi
argus-restart    # Restart usługi
argus-logs       # Podgląd logów
argus-diag       # Diagnostyka systemu
```

## 🔌 Usługi i porty

- **ROS2:** Domain ID 42, localhost only
- **RTSP Stream:** `rtsp://***********:8554/camera_stream`
- **HTTP:** Port 8080
- **SSH:** Port 22

## 🐛 Rozwiązywanie problemów

### Brak połączenia z Access Point
```bash
sudo systemctl status hostapd
sudo systemctl status dnsmasq
```

### Problemy z ROS2
```bash
source /opt/ros/jazzy/setup.bash
ros2 node list
ros2 topic list
```

### Problemy z kamerą
```bash
v4l2-ctl --list-devices
ls -la /dev/video*
```

### Logi systemowe
```bash
sudo journalctl -u argus_core -n 50
tail -f /var/log/argus/*.log
```

## 🔒 Bezpieczeństwo

- ROS2 skonfigurowany tylko dla localhost
- Access Point z WPA2
- SSH włączony (zmień domyślne hasło!)
- Firewall opcjonalny (domyślnie wyłączony)

## 📞 Wsparcie

W przypadku problemów sprawdź:
1. Logi instalacji: `/var/log/argus/`
2. Status usług: `systemctl status argus_core`
3. Diagnostykę: `argus-diag`

---

**⚠️ UWAGA:** Podczas instalacji nastąpi rozłączenie z siecią WiFi. Zalecane jest użycie połączenia LAN.
