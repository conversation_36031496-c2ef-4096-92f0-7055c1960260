# 🦊 GitLab - Konfiguracja dla ARGUS Robot

## 📋 Przygotowanie repozytorium GitLab

### 1. Utwórz repozytorium na self-hosted GitLab
1. Zaloguj się na swój GitLab (np. `https://gitlab.devforyou.pl`)
2. <PERSON>lik<PERSON>j **"New project"**
3. <PERSON><PERSON><PERSON><PERSON> **"Create blank project"**
4. Nazwa: `robot_core`
5. <PERSON><PERSON><PERSON> (Public/Private)

### 2. Konfiguracja dla self-hosted GitLab
Self-hosted GitLab może wymagać dodatkowej konfiguracji:
- Sprawdź czy używa HTTPS z prawidłowymi certyfikatami
- Upewnij się, że port jest dostępny (domyślnie 80/443)
- Sprawd<PERSON> czy firewall nie blokuje połączeń

### 3. Przenieś kod robot_core do GitLab
```bash
# W katalogu robot_core
cd robot_core
git init
git add .
git commit -m "Initial commit - ARGUS robot_core"
git remote add origin https://gitlab.devforyou.pl/avotech/robot_core.git
git push -u origin main
```

## ⚙️ Konfiguracja provisioning

### 1. Edytuj plik konfiguracyjny
```bash
nano config/gitlab.conf
```

### 2. Ustaw właściwy URL dla self-hosted GitLab
```bash
# Dla repozytorium publicznego
ROBOT_CORE_REPO_URL="https://gitlab.devforyou.pl/avotech/robot_core.git"

# Dla repozytorium prywatnego
ROBOT_CORE_REPO_URL="https://gitlab.devforyou.pl/avotech/robot_core.git"
GITLAB_USERNAME="twoja-nazwa"
GITLAB_TOKEN="twoj-personal-access-token"

# Konfiguracja dla self-hosted
GITLAB_SELF_HOSTED=true
GIT_SSL_VERIFY=true  # Ustaw false jeśli problemy z certyfikatami
```

### 3. Utwórz Personal Access Token (dla repo prywatnych)
1. GitLab → **Settings** → **Access Tokens**
2. Nazwa: `argus-robot-provisioning`
3. Scopes: `read_repository`
4. Skopiuj token do `config/gitlab.conf`

## 🔐 Uwierzytelnianie SSH (alternatywa)

### 1. Wygeneruj klucz SSH na Raspberry Pi
```bash
ssh-keygen -t rsa -b 4096 -C "argus-robot@raspberry"
cat ~/.ssh/id_rsa.pub
```

### 2. Dodaj klucz do GitLab
1. GitLab → **Settings** → **SSH Keys**
2. Wklej zawartość `id_rsa.pub`

### 3. Użyj SSH URL w konfiguracji
```bash
ROBOT_CORE_REPO_URL="**************:twoja-nazwa/robot_core.git"
```

## 📁 Struktura repozytorium robot_core

Upewnij się, że repozytorium ma właściwą strukturę ROS2:

```
robot_core/
├── camera_node/
│   ├── package.xml
│   ├── setup.py
│   └── camera_node/
├── sensors/
│   ├── package.xml
│   ├── setup.py
│   └── sensors/
├── py_motors/
│   ├── package.xml
│   ├── setup.py
│   └── py_motors/
├── oled_subscriber/
│   ├── package.xml
│   ├── setup.py
│   └── oled_subscriber/
├── interfaces/
│   ├── package.xml
│   ├── CMakeLists.txt
│   └── srv/
└── robot_core/          # Główny pakiet z launch files
    ├── package.xml
    ├── CMakeLists.txt
    └── launch/
        └── argus_robot.launch.py
```

## 🔄 Workflow rozwoju

### 1. Rozwój lokalny
```bash
# Edytuj kod w /root/src_ros2/robot_core
cd /root/src_ros2/robot_core
# ... zmiany w kodzie ...

# Commit i push
git add .
git commit -m "Update sensor calibration"
git push
```

### 2. Aktualizacja na robocie
```bash
# Pull najnowszych zmian
cd /root/src_ros2/robot_core
git pull

# Rebuild ROS2 workspace
cd /root/ros2_jazzy
colcon build --symlink-install

# Restart usługi
sudo systemctl restart argus_core
```

## 🐛 Rozwiązywanie problemów

### Błąd klonowania z self-hosted GitLab
```bash
# Sprawdź połączenie z self-hosted GitLab
ping gitlab.devforyou.pl

# Sprawdź URL
git ls-remote https://gitlab.devforyou.pl/avotech/robot_core.git

# Test HTTPS połączenia
curl -I https://gitlab.devforyou.pl

# Sprawdź dane uwierzytelniające
git config --global user.name "Twoja Nazwa"
git config --global user.email "<EMAIL>"
```

### Problemy z certyfikatami SSL
```bash
# Jeśli self-hosted GitLab używa self-signed certificates
git config --global http.sslVerify false

# Lub w pliku gitlab.conf:
********************
```

### Problemy z timeoutami
```bash
# Zwiększ timeout w gitlab.conf:
GIT_TIMEOUT=600
GIT_HTTP_LOW_SPEED_TIME=120
```

### Problemy z tokenem
```bash
# Test tokena
curl -H "PRIVATE-TOKEN: twoj-token" https://gitlab.com/api/v4/user
```

### Problemy z SSH
```bash
# Test połączenia SSH
ssh -T **************

# Sprawdź klucze
ls -la ~/.ssh/
```

---

**💡 Tip:** Użyj branch `develop` do rozwoju i `main` do stabilnych wersji!
