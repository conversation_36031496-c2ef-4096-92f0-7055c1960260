# ARGUS Robot - Przydatne komendy

## Instalacja

```bash
# Pełna instalacja (zalecane)
sudo ./install.sh

# S<PERSON>bka instalacja wybranych komponentów
sudo ./quick_install.sh

# Test instalacji
sudo ./test_install.sh
```

## Zarządzanie usługami

```bash
# Status głównej usługi
sudo systemctl status argus_core

# Start/Stop/Restart
sudo systemctl start argus_core
sudo systemctl stop argus_core
sudo systemctl restart argus_core

# Włączenie/wyłączenie autostartu
sudo systemctl enable argus_core
sudo systemctl disable argus_core

# Logi usługi
sudo journalctl -u argus_core -f
sudo journalctl -u argus_core -n 50
```

## ROS2

```bash
# Source środowiska (jako root)
source /opt/ros/jazzy/setup.bash
source /root/ros2_jazzy/install/setup.bash

# Lista węzłów
ros2 node list

# Lista topików
ros2 topic list

# Informacje o topiku
ros2 topic info /camera/image_raw

# Echo topiku
ros2 topic echo /sensors/temperature

# Budowanie workspace
cd /root/ros2_jazzy
colcon build --symlink-install
```

## Zarządzanie kodem źródłowym

```bash
# Aktualizacja kodu z GitLab
cd /root/src_ros2/robot_core
git pull

# Sprawdzenie statusu repozytorium
git status
git log --oneline -10

# Przełączenie na inny branch
git checkout develop

# Rebuild po zmianach w kodzie
cd /root/ros2_jazzy
colcon build --symlink-install

# Sprawdzenie struktury katalogów
ls -la /root/src_ros2/
ls -la /root/ros2_jazzy/src/
```

## Diagnostyka

```bash
# Skrypt diagnostyczny
sudo /opt/argus/bin/argus_diagnostics.sh

# Status sieci
ip addr show
iwconfig

# Status kamer
v4l2-ctl --list-devices
ls -la /dev/video*

# Temperatura CPU
vcgencmd measure_temp

# Status throttling
vcgencmd get_throttled

# Użycie dysku
df -h

# Procesy
htop
ps aux | grep ros
```

## Sieć

```bash
# Status Access Point
sudo systemctl status hostapd
sudo systemctl status dnsmasq

# Restart sieci
sudo systemctl restart hostapd
sudo systemctl restart dnsmasq

# Konfiguracja IP
sudo ip addr add ***********/24 dev wlan0

# Skanowanie sieci WiFi
sudo iwlist wlan0 scan | grep ESSID
```

## Logi

```bash
# Logi ARGUS
tail -f /var/log/argus/*.log
ls -la /var/log/argus/

# Logi systemowe
sudo journalctl -f
sudo journalctl -u argus_core
sudo journalctl -u hostapd
sudo journalctl -u dnsmasq

# Logi kernela
dmesg | tail
```

## Kamera i streaming

```bash
# Test kamery
raspistill -o test.jpg
ffmpeg -f v4l2 -i /dev/video0 -t 5 test.mp4

# RTSP stream
ffmpeg -f v4l2 -i /dev/video0 -f rtsp rtsp://localhost:8554/stream

# Lista formatów kamery
v4l2-ctl --list-formats-ext
```

## Backup i restore

```bash
# Backup konfiguracji
sudo tar -czf argus_backup.tar.gz /root/ros2_jazzy /root/src_ros2 /opt/argus /etc/argus

# Backup obrazu SD (z innego komputera)
sudo dd if=/dev/sdX of=argus_robot_backup.img bs=4M status=progress

# Restore obrazu SD
sudo dd if=argus_robot_backup.img of=/dev/sdX bs=4M status=progress
```

## Aktualizacja

```bash
# Aktualizacja systemu
sudo apt update && sudo apt upgrade -y

# Aktualizacja ROS2 pakietów
sudo apt update && sudo apt upgrade ros-jazzy-*

# Rebuild workspace po zmianach
cd /root/argus_ws
rm -rf build install log
colcon build --symlink-install
```

## Rozwiązywanie problemów

```bash
# Reset konfiguracji sieci
sudo systemctl stop hostapd dnsmasq
sudo systemctl start hostapd dnsmasq

# Reset ROS2 workspace
cd /root/ros2_jazzy
rm -rf build install log
source /opt/ros/jazzy/setup.bash
colcon build

# Sprawdzenie błędów w logach
sudo journalctl -p err
grep -r "ERROR" /var/log/argus/

# Reset do ustawień fabrycznych (UWAGA!)
sudo systemctl stop argus_core
sudo rm -rf /root/ros2_jazzy/build /root/ros2_jazzy/install
sudo rm -rf /root/src_ros2/robot_core
sudo ./install.sh
```
