# 🚀 ARGUS Robot - <PERSON><PERSON>b<PERSON> Start

## Dla nowych użytkowników

### 1. Przygotowanie Raspberry Pi
- Zainstaluj **Raspbian Bookworm** na karcie microSD (min. 32GB)
- Włącz SSH w raspi-config
- Podłącz Raspberry Pi do sieci LAN (Ethernet)

### 2. Pobranie i instalacja
```bash
# Zaloguj się na Raspberry Pi
ssh argus@<IP_RASPBERRY_PI>
sudo su

# Pobierz projekt
git clone <repository-url>
cd robot_provisioning

# Opcjonalnie: skonfiguruj GitLab
nano config/gitlab.conf

# Uruchom instalację
sudo ./install.sh
```

### 3. Czekaj na zakończenie
- **NIE PRZERYWAJ** procesu
- System zostanie automatycznie zrestartowany

### 4. Połącz się z robotem
Po restarcie robot będzie dostępny jako Access Point:
- **SSID:** `ARGUS`
- **Hasło:** `argusik!23`
- **IP:** `***********`

```bash
# Połącz się przez SSH
ssh argusik@***********
```


### Test instalacji
```bash
sudo ./test_install.sh
```

### Diagnostyka
```bash
sudo /opt/argus/bin/argus_diagnostics.sh
```

## Pierwsze kroki po instalacji

### 1. Sprawdź status
```bash
# Status głównej usługi
sudo systemctl status argus_core

# Lista węzłów ROS2
ros2 node list
```

### 2. Sprawdź kamerę
```bash
# Lista kamer
v4l2-ctl --list-devices

# Test kamery
raspistill -o test.jpg
```

### 3. Sprawdź streaming
```bash
# RTSP stream dostępny na:
rtsp://***********:8554/camera_stream
```

## Rozwiązywanie problemów

### Robot nie uruchamia się
```bash
sudo journalctl -u argus_core -f
```

### Brak Access Point
```bash
sudo systemctl status hostapd
sudo systemctl restart hostapd
```

### Problemy z ROS2
```bash
source /opt/ros/jazzy/setup.bash
ros2 doctor
```

## Przydatne aliasy

Po instalacji dostępne są aliasy:
```bash
argus-status    # Status usługi
argus-restart   # Restart usługi  
argus-logs      # Podgląd logów
argus-diag      # Diagnostyka
```

## Wsparcie

- 📖 Pełna dokumentacja: `README.md`
- 🔧 Komendy: `COMMANDS.md`
- 🐛 Problemy: Sprawdź logi w `/var/log/argus/`

---

**⚠️ PAMIĘTAJ:** Zmień domyślne hasła po instalacji!
