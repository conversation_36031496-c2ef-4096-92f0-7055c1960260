#!/bin/bash
# test_install.sh - Skrypt testowy do sprawdzenia instalacji

set -e

echo "=== ARGUS Robot - Test instalacji ==="

# Kolory
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

test_passed=0
test_failed=0

# Funkcja testująca
test_check() {
    local test_name="$1"
    local command="$2"
    
    echo -n "Testowanie: $test_name... "
    
    if eval "$command" &>/dev/null; then
        echo -e "${GREEN}PASS${NC}"
        ((test_passed++))
        return 0
    else
        echo -e "${RED}FAIL${NC}"
        ((test_failed++))
        return 1
    fi
}

echo "Sprawdzanie podstawowych komponentów..."

# Test systemu
test_check "System Raspbian Bookworm" "grep -q 'bookworm' /etc/os-release"
test_check "Użytkownik root" "[ \$(id -u) -eq 0 ]"

# Test katalogów
test_check "Katalog /root/argus_ws" "[ -d /root/argus_ws ]"
test_check "Katalog /opt/argus" "[ -d /opt/argus ]"
test_check "Katalog /var/log/argus" "[ -d /var/log/argus ]"

# Test ROS2
test_check "ROS2 Jazzy zainstalowany" "[ -d /opt/ros/jazzy ]"
test_check "Colcon zainstalowany" "command -v colcon"

# Test Python bibliotek
test_check "OpenCV Python" "python3 -c 'import cv2'"
test_check "NumPy" "python3 -c 'import numpy'"
test_check "RPi.GPIO" "python3 -c 'import RPi.GPIO'"

# Test usług
test_check "Usługa argus_core" "systemctl is-enabled argus_core"
test_check "Usługa hostapd" "systemctl is-enabled hostapd"
test_check "Usługa dnsmasq" "systemctl is-enabled dnsmasq"

# Test plików
test_check "Skrypt startowy ROS2" "[ -f /opt/argus/bin/start_ros2.sh ]"
test_check "Skrypt diagnostyczny" "[ -f /opt/argus/bin/argus_diagnostics.sh ]"

# Test konfiguracji
test_check "Konfiguracja hostapd" "[ -f /etc/hostapd/hostapd.conf ]"
test_check "Konfiguracja dnsmasq" "[ -f /etc/dnsmasq.conf ]"

echo ""
echo "=== Wyniki testów ==="
echo -e "Testy przeszły: ${GREEN}$test_passed${NC}"
echo -e "Testy nie przeszły: ${RED}$test_failed${NC}"

if [ $test_failed -eq 0 ]; then
    echo -e "${GREEN}Wszystkie testy przeszły pomyślnie!${NC}"
    echo "System ARGUS Robot jest gotowy do użycia."
    exit 0
else
    echo -e "${RED}Niektóre testy nie przeszły.${NC}"
    echo "Sprawdź logi instalacji i uruchom ponownie install.sh"
    exit 1
fi
